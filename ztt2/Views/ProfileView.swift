//
//  ProfileView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/2.
//

import SwiftUI
// 注意：暂时注释掉RevenueCat，因为当前项目可能还没有这个依赖
// import RevenueCat

/**
 * 个人中心主视图
 * 包含个人信息、订阅标签和系统设置三个主要部分
 */
struct ProfileView: View {

    // MARK: - Dependencies
    // 注意：暂时注释掉AuthenticationManager，因为当前项目可能还没有这个管理器
    // @EnvironmentObject var authManager: AuthenticationManager
    @ObservedObject private var syncManager = iCloudSyncManager.shared
    @ObservedObject private var dataManager = DataManager.shared

    // MARK: - State
    @State private var pageAppeared = false
    @State private var showSubscriptionView = false // 控制订阅页面显示
    @State private var showProductIntroduction = false // 控制产品介绍页面显示
    @State private var showFeedbackAlert = false // 控制帮助与反馈弹窗
    @State private var showAboutView = false // 控制关于页面显示

    // iCloud同步相关状态
    @State private var showSyncPermissionAlert = false // 权限不足弹窗
    @State private var showSyncConfirmAlert = false // 同步确认弹窗
    @State private var showSyncErrorAlert = false // 同步错误弹窗
    @State private var pendingSyncAction: SyncAction? = nil // 待执行的同步操作

    // 降级处理相关状态
    @State private var showDowngradeBanner = false // 显示降级横幅
    @State private var isDowngraded = false // 是否已降级

    // 试用期提醒弹窗状态
    @State private var showTrialReminderModal = false // 控制试用期订阅提醒弹窗

    // 开发测试相关状态
    @State private var showMembershipTestButton = true // 显示会员等级测试按钮（仅开发阶段）
    @State private var showMembershipSelector = false // 显示会员等级选择器

// MARK: - Sync Action Enum
enum SyncAction {
    case enable
    case disable
}

    // 添加管理器（暂时注释掉，因为当前项目可能还没有这些管理器）
    // @StateObject private var subscriptionManager = CloudKitSubscriptionManager.shared
    // @StateObject private var accountDeletionManager = AccountDeletionManager.shared
    // @StateObject private var trialManager = TrialManager.shared
    // @StateObject private var revenueCatManager = RevenueCatManager.shared

    // MARK: - Computed Properties
    private var userName: String {
        // 暂时使用固定值，后续可以从AuthenticationManager获取
        return "user_info.parent_nickname".localized
    }

    private var userID: String {
        // 暂时使用固定值，后续可以从AuthenticationManager获取
        return "profile.no_email".localized
    }

    private var membershipLevel: String {
        guard let user = dataManager.currentUser else {
            return "subscription.user_level_regular".localized
        }

        switch user.subscriptionType {
        case "premium":
            return "高级会员"
        case "basic":
            return "初级会员"
        default:
            return "免费用户"
        }
    }

    private var expirationDate: String {
        guard let user = dataManager.currentUser,
              let subscription = user.subscription,
              subscription.isActive,
              let endDate = subscription.endDate else {
            return "subscription.not_activated".localized
        }

        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: endDate)
    }

    var body: some View {
        let backgroundGradient = LinearGradient(
            gradient: Gradient(stops: [
                .init(color: Color(hex: "#fcfff4"), location: 0.0),
                .init(color: Color(hex: "#f8fdf0"), location: 0.3),
                .init(color: Color.white, location: 0.7),
                .init(color: Color(hex: "#fafffe"), location: 1.0)
            ]),
            startPoint: .top,
            endPoint: .bottom
        )

        return ZStack {
            // 美化背景渐变 - 与其他页面保持一致的风格
            backgroundGradient
                .ignoresSafeArea(.all)

            // 装饰性背景元素
            VStack {
                HStack {
                    Circle()
                        .fill(Color(hex: "#B5E36B").opacity(0.03))
                        .frame(width: 100, height: 100)
                        .offset(x: -30, y: 20)
                    Spacer()
                    Circle()
                        .fill(Color(hex: "#FFE49E").opacity(0.04))
                        .frame(width: 120, height: 120)
                        .offset(x: 40, y: -10)
                }
                Spacer()
                HStack {
                    Spacer()
                    Circle()
                        .fill(Color(hex: "#B5E36B").opacity(0.02))
                        .frame(width: 80, height: 80)
                        .offset(x: 20, y: 30)
                }
            }

            // 主要内容区域 - 自适应布局
            GeometryReader { geometry in
                VStack(spacing: 0) {
                    // 个人信息组件 - 紧贴屏幕顶部和两侧，使用屏幕高度百分比
                    let userInfoAnimation = Animation.spring(response: 0.8, dampingFraction: 0.8).delay(0.1)

                    UserInfoSection(
                        userName: userName,
                        userID: userID,
                        membershipLevel: membershipLevel,
                        expirationDate: expirationDate
                    )
                    .frame(height: geometry.size.height * 0.25) // 使用屏幕高度的25%
                    .frame(maxWidth: .infinity) // 紧贴屏幕两侧
                    .opacity(pageAppeared ? 1.0 : 0.0)
                    .offset(y: pageAppeared ? 0 : 50)
                    .animation(userInfoAnimation, value: pageAppeared)

                    // 订阅标签组件 - 叠加在个人信息组件底部
                    let subscriptionAnimation = Animation.spring(response: 0.8, dampingFraction: 0.8).delay(0.3)

                    SubscriptionBannerSection {
                        handleViewPlansPressed()
                    }
                    .padding(.horizontal, DesignSystem.Spacing.pageHorizontal)
                    .offset(y: -30) // 叠加效果：向上偏移30pt
                    .opacity(pageAppeared ? 1.0 : 0.0)
                    .animation(subscriptionAnimation, value: pageAppeared)

                    // 可滚动内容区域
                    ScrollView(.vertical, showsIndicators: false) {
                        VStack(spacing: 0) {
                            // 降级横幅（如果需要显示）
                            if showDowngradeBanner {
                                DowngradeBannerView(
                                    onRenewTapped: {
                                        showSubscriptionView = true
                                    },
                                    onDismiss: {
                                        withAnimation(.easeOut(duration: 0.3)) {
                                            showDowngradeBanner = false
                                        }
                                    }
                                )
                                .padding(.horizontal, DesignSystem.Spacing.pageHorizontal)
                                .padding(.top, 10)
                                .transition(.move(edge: .top).combined(with: .opacity))
                            }

                            // 顶部空间给订阅标签预留
                            Spacer()
                                .frame(height: 0)

                            // 系统设置组件
                            SystemSettingsSection(
                                onSettingItemPressed: { settingType in
                                    handleSettingItemPressed(settingType)
                                },
                                onToggleChanged: { settingType, isEnabled in
                                    handleToggleChanged(settingType, isEnabled)
                                },
                                syncManager: syncManager,
                                iCloudSyncEnabled: $syncManager.isSyncEnabled
                            )
                            .padding(.horizontal, DesignSystem.Spacing.pageHorizontal)
                            .padding(.top, 50) // 为订阅标签留出空间
                            .opacity(pageAppeared ? 1.0 : 0.0)
                            .offset(y: pageAppeared ? -50 : -10) // 向上移动50个点
                            .animation(Animation.spring(response: 0.8, dampingFraction: 0.8).delay(0.5), value: pageAppeared)

                            // 底部空白区域，确保内容不被导航栏遮挡
                            Spacer()
                                .frame(height: DesignSystem.LiquidTabBar.height + 40)
                        }
                    }
                }
            }

            // 开发测试悬浮按钮 - 右下角
            if showMembershipTestButton {
                VStack {
                    Spacer()
                    HStack {
                        Spacer()
                        MembershipTestFloatingButton(
                            currentLevel: getCurrentMembershipLevel(),
                            onLevelChanged: { newLevel in
                                changeMembershipLevel(to: newLevel)
                            }
                        )
                        .padding(.trailing, DesignSystem.Spacing.md)
                        .padding(.bottom, DesignSystem.LiquidTabBar.height + DesignSystem.Spacing.md)
                    }
                }
            }
        }
        .onAppear {
            // 页面入场动画
            withAnimation {
                pageAppeared = true
            }
            // 加载用户数据（为后续功能预留）
            loadUserData()
        }
        // 订阅页面全屏展示
        .fullScreenCover(isPresented: $showSubscriptionView, onDismiss: {
            // 从订阅页面返回时重新加载用户数据，确保显示最新的订阅状态
            loadUserData()
        }) {
            SubscriptionView {
                // 订阅页面的返回回调
                showSubscriptionView = false
            }
        }
        .fullScreenCover(isPresented: $showProductIntroduction) {
            ProductIntroductionView()
        }
        .sheet(isPresented: $showAboutView) {
            AboutView()
        }
        .alert("feedback.contact_email.title".localized, isPresented: $showFeedbackAlert) {
            Button("feedback.contact_email.confirm".localized, role: .cancel) {
                // 确定按钮，关闭弹窗
            }
        } message: {
            Text("feedback.contact_email.message".localized)
        }
        // iCloud同步权限不足弹窗
        .alert("icloud_sync.alert.permission_title".localized, isPresented: $showSyncPermissionAlert) {
            Button("icloud_sync.button.subscribe".localized) {
                showSubscriptionView = true
            }
            Button("icloud_sync.button.cancel".localized, role: .cancel) {
                pendingSyncAction = nil
            }
        } message: {
            Text("icloud_sync.alert.permission_message".localized)
        }
        // iCloud同步确认弹窗
        .alert(
            pendingSyncAction == .enable ? "icloud_sync.alert.enable_title".localized : "icloud_sync.alert.disable_title".localized,
            isPresented: $showSyncConfirmAlert
        ) {
            Button(pendingSyncAction == .enable ? "icloud_sync.button.enable".localized : "icloud_sync.button.disable".localized) {
                executeSync()
            }
            Button("icloud_sync.button.cancel".localized, role: .cancel) {
                pendingSyncAction = nil
            }
        } message: {
            Text(pendingSyncAction == .enable ? "icloud_sync.alert.enable_message".localized : "icloud_sync.alert.disable_message".localized)
        }
        // iCloud同步错误弹窗
        .alert("icloud_sync.alert.no_icloud_title".localized, isPresented: $showSyncErrorAlert) {
            Button("icloud_sync.button.settings".localized) {
                openSystemSettings()
            }
            Button("icloud_sync.button.retry".localized) {
                Task {
                    await syncManager.checkAvailability()
                    if syncManager.isAvailable {
                        showSyncConfirmAlert = true
                    } else {
                        showSyncErrorAlert = true
                    }
                }
            }
            Button("icloud_sync.button.cancel".localized, role: .cancel) {
                pendingSyncAction = nil
            }
        } message: {
            Text(syncManager.errorMessage ?? "icloud_sync.alert.no_icloud_message".localized)
        }
        // 处理订阅降级通知
        .handleSubscriptionChanges()
        .onReceive(NotificationCenter.default.publisher(for: .subscriptionDowngraded)) { _ in
            withAnimation(.easeInOut(duration: 0.5)) {
                showDowngradeBanner = true
                isDowngraded = true
            }
        }



        // 暂时注释掉删除进度显示，因为当前项目可能还没有AccountDeletionProgressView
        /*
        .overlay(
            showDeleteAccountProgress ?
            AccountDeletionProgressView(
                deletionManager: accountDeletionManager,
                onCancel: {
                    showDeleteAccountProgress = false
                    resetDeleteAccountState()
                },
                onComplete: {
                    showDeleteAccountProgress = false
                    resetDeleteAccountState()

                    // 删除完成后跳转到登录页面
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        authManager.logout()
                    }
                },
                onError: { error in
                    deletionError = error
                    showDeleteAccountProgress = false
                    resetDeleteAccountState()
                }
            ) : nil
        )
        */

        // 暂时注释掉试用期订阅提醒弹窗，因为当前项目可能还没有TrialSubscriptionReminderModal
        /*
        .overlay(
            showTrialReminderModal ?
            TrialSubscriptionReminderModal(isPresented: $showTrialReminderModal) {
                // 用户点击"谢谢提醒"后，关闭弹窗并跳转到订阅页面
                print("🔔 用户确认试用期提醒，跳转到订阅页面")
                showSubscriptionView = true
            } : nil
        )
        */

    }


}

// MARK: - Settings Row Component (删除这个重复的组件)
/*
private struct SettingsRow: View {
    let icon: String
    let title: String
    let titleColor: Color
    let action: () -> Void

    init(icon: String, title: String, titleColor: Color = DesignSystem.Colors.profileSettingsTextColor, action: @escaping () -> Void) {
        self.icon = icon
        self.title = title
        self.titleColor = titleColor
        self.action = action
    }

    var body: some View {
        Button(action: action) {
            HStack(spacing: DesignSystem.Spacing.md) {
                Image(systemName: icon)
                    .frame(width: 20, height: 20)
                    .foregroundColor(DesignSystem.Colors.profileSettingsIconColor)

                Text(title)
                    .font(.system(
                        size: DesignSystem.Typography.Body.fontSize,
                        weight: DesignSystem.Typography.Body.fontWeight
                    ))
                    .foregroundColor(titleColor)

                Spacer()

                Image(systemName: "chevron.right")
                    .foregroundColor(DesignSystem.Colors.profileSettingsArrowColor)
            }
            .padding(.horizontal, DesignSystem.Spacing.md)
            .padding(.vertical, DesignSystem.Spacing.md)
        }
        .buttonStyle(PlainButtonStyle())
    }
}
*/

// MARK: - ProfileView Extension for Action Handlers
extension ProfileView {

    // MARK: - Action Handlers

    /**
     * 处理查看会员方案按钮点击
     */
    private func handleViewPlansPressed() {
        print("查看会员方案功能")

        // 暂时直接显示订阅页面，后续可以根据试用状态处理
        withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
            showSubscriptionView = true
        }

        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }

    /**
     * 处理设置项点击
     */
    private func handleSettingItemPressed(_ settingType: SettingType) {
        print("设置项被点击: \(settingType.displayName)")
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            // 添加点击反馈动画
        }

        switch settingType {
        case .iCloudSync:
            // 处理iCloud同步
            // TODO: 实现iCloud同步功能
            print("iCloud同步功能")
        case .productIntroduction:
            // 显示产品介绍页面
            showProductIntroduction = true
            print("显示产品介绍页面")
        case .feedback:
            // 显示帮助与反馈弹窗
            showFeedbackAlert = true
            print("显示帮助与反馈弹窗")
        case .about:
            // 显示关于页面
            showAboutView = true
            print("显示关于页面")
        case .clearAllData:
            // 直接清除所有数据，不显示弹窗
            clearAllData()
            print("清除所有数据")
        }
    }

    /**
     * 处理开关状态变化
     */
    private func handleToggleChanged(_ settingType: SettingType, _ isEnabled: Bool) {
        print("开关状态改变: \(settingType.displayName) -> \(isEnabled)")

        switch settingType {
        case .iCloudSync:
            handleiCloudSyncToggle(isEnabled)
        default:
            break
        }
    }

    /**
     * 处理iCloud同步开关切换
     */
    private func handleiCloudSyncToggle(_ isEnabled: Bool) {
        // 添加触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()

        // 首先检查权限
        if !syncManager.hasPermission() {
            showSyncPermissionAlert = true
            return
        }

        if isEnabled {
            // 尝试开启同步
            pendingSyncAction = .enable

            // 检查iCloud可用性
            Task {
                await syncManager.checkAvailability()

                await MainActor.run {
                    if syncManager.isAvailable {
                        showSyncConfirmAlert = true
                    } else {
                        showSyncErrorAlert = true
                    }
                }
            }
        } else {
            // 关闭同步
            pendingSyncAction = .disable
            showSyncConfirmAlert = true
        }
    }

    /**
     * 处理退出登录
     * 可作为替代方案使用，当前在alert回调中直接调用logout方法
     */
    private func handleLogout() {
        print("用户确认退出登录")

        // 触觉反馈
        let feedbackGenerator = UINotificationFeedbackGenerator()
        feedbackGenerator.notificationOccurred(.success)

        // 添加过渡动画
        withAnimation(.easeInOut(duration: 0.3)) {
            // 暂时注释掉，因为当前项目可能还没有AuthenticationManager
            // authManager.logout()
            print("退出登录功能暂未实现")
        }
    }

    /**
     * 加载用户数据
     * 在页面出现时调用，用于加载和刷新用户信息
     */
    private func loadUserData() {
        // 此处可以添加额外的用户数据加载逻辑
        // 例如从网络或本地数据库获取最新的用户信息
        print("加载用户数据：\(userName)")

        // 暂时注释掉，因为当前项目可能还没有这些管理器
        // trialManager.refreshTrialStatus()

        // 触发订阅状态检查
        // Task {
        //     try? await Purchases.shared.customerInfo()
        // }
    }

    /**
     * 清除所有数据
     */
    private func clearAllData() {
        print("🗑️ 清除所有数据")

        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
        impactFeedback.impactOccurred()

        // TODO: 实现清除所有数据的逻辑
        // 这里可以清除Core Data中的所有数据
        // 清除UserDefaults中的设置
        // 清除缓存文件等

        print("清除所有数据功能暂未实现")
    }

    /**
     * 执行同步操作
     */
    private func executeSync() {
        guard let action = pendingSyncAction else { return }

        Task {
            switch action {
            case .enable:
                let success = await syncManager.enableSync()
                if !success {
                    await MainActor.run {
                        showSyncErrorAlert = true
                    }
                }
            case .disable:
                await MainActor.run {
                    syncManager.disableSync()
                }
            }

            await MainActor.run {
                pendingSyncAction = nil
            }
        }
    }

    /**
     * 打开系统设置
     */
    private func openSystemSettings() {
        if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(settingsUrl)
        }
        pendingSyncAction = nil
    }

    // MARK: - 开发测试方法

    /**
     * 获取当前会员等级
     */
    private func getCurrentMembershipLevel() -> String {
        guard let user = dataManager.currentUser else { return "free" }
        return user.subscriptionType
    }

    /**
     * 切换会员等级（仅用于开发测试）
     */
    private func changeMembershipLevel(to level: String) {
        print("🔄 切换会员等级到: \(level)")

        // 计算到期时间
        let endDate: Date?
        if level == "free" {
            endDate = nil
        } else {
            // 付费会员设置1年后到期
            endDate = Calendar.current.date(byAdding: .year, value: 1, to: Date())
        }

        // 更新订阅状态
        dataManager.updateSubscription(
            type: level,
            isActive: level != "free",
            startDate: level != "free" ? Date() : nil,
            endDate: endDate,
            productIdentifier: level == "premium" ? "com.ztt.premium.yearly" : (level == "basic" ? "com.ztt.basic.yearly" : nil)
        )

        // 显示切换结果
        let levelName = getLevelDisplayName(level)
        print("✅ 会员等级已切换到: \(levelName)")

        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }

    /**
     * 获取等级显示名称
     */
    private func getLevelDisplayName(_ level: String) -> String {
        switch level {
        case "free":
            return "免费用户"
        case "basic":
            return "初级会员"
        case "premium":
            return "高级会员"
        default:
            return "未知等级"
        }
    }
}

// MARK: - Preview
#Preview {
    ProfileView()
        // 暂时注释掉，因为当前项目可能还没有AuthenticationManager
        // .environmentObject(AuthenticationManager())
        // .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}

// MARK: - Preview
#Preview {
    ProfileView()
}
